package utils

import (
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"time"
)

// DelAndCreateDirectory removes the directory if it exists and creates a new one.
// It returns an error if the directory removal or creation fails.
// dirPath: the path of the directory to be remade
func DelAndCreateDirectory(dirPath string) error {
	// 检查目录是否存在
	if _, err := os.Stat(dirPath); !os.IsNotExist(err) {
		// 目录存在，删除它
		if err := os.RemoveAll(dirPath); err != nil {
			return fmt.Errorf("删除已存在的目录失败: %v", err)
		}
	}
	return CreateDirectory(dirPath)
}

// CreateDirectory checks if the directory exists and creates it if it doesn't.
// It returns an error if the directory creation fails.
// dirPath: the path of the directory to be created
func CreateDirectory(dirPath string) error {
	// 如果目录不存在，创建目录
	if err := os.MkdirAll(dirPath, 0755); err != nil {
		return fmt.Errorf("创建目录失败 [%s]: %v", dirPath, err)
	}
	return nil
}

func MoveToTargetDir(srcPath, targetDir string) {
	fileName := filepath.Base(srcPath)
	newPath := filepath.Join(targetDir, fileName)

	// 添加重试机制，特别针对 Windows 系统的文件占用问题
	maxRetries := 5
	for i := 0; i < maxRetries; i++ {
		if err := os.Rename(srcPath, newPath); err != nil {
			if i == maxRetries-1 {
				// 最后一次重试失败，尝试复制+删除的方式
				log.Printf("重命名失败，尝试复制+删除方式移动文件 %s to %s", fileName, targetDir)
				if copyErr := copyAndDeleteFile(srcPath, newPath); copyErr != nil {
					log.Printf("移动文件 %s to %s 文件失败: %v\n", fileName, targetDir, err)
					return
				}
				log.Printf("成功移动文件 %s to %s (使用复制+删除方式)", fileName, targetDir)
				return
			}
			// 等待一段时间后重试
			log.Printf("移动文件失败，第 %d 次重试: %v", i+1, err)
			time.Sleep(time.Millisecond * 100 * time.Duration(i+1)) // 递增等待时间
			continue
		}
		// 成功移动
		if i > 0 {
			log.Printf("成功移动文件 %s to %s (第 %d 次重试)", fileName, targetDir, i+1)
		}
		return
	}
}

// copyAndDeleteFile 复制文件然后删除原文件
func copyAndDeleteFile(srcPath, dstPath string) error {
	// 打开源文件
	srcFile, err := os.Open(srcPath)
	if err != nil {
		return fmt.Errorf("打开源文件失败: %v", err)
	}
	defer srcFile.Close()

	// 创建目标文件
	dstFile, err := os.Create(dstPath)
	if err != nil {
		return fmt.Errorf("创建目标文件失败: %v", err)
	}
	defer dstFile.Close()

	// 复制文件内容
	if _, err := io.Copy(dstFile, srcFile); err != nil {
		return fmt.Errorf("复制文件内容失败: %v", err)
	}

	// 确保数据写入磁盘
	if err := dstFile.Sync(); err != nil {
		return fmt.Errorf("同步文件失败: %v", err)
	}

	// 关闭文件句柄
	srcFile.Close()
	dstFile.Close()

	// 删除源文件
	if err := os.Remove(srcPath); err != nil {
		return fmt.Errorf("删除源文件失败: %v", err)
	}

	return nil
}
