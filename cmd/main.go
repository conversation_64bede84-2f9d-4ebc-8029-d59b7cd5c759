package main

import (
	"QuestionsDownloader/internal/jkd_chap_tree"
	"QuestionsDownloader/internal/jkd_dwld"
	"QuestionsDownloader/internal/jkd_image"
	"QuestionsDownloader/internal/jkd_import"
	"QuestionsDownloader/pkg/utils"
	"fmt"
	"github.com/joho/godotenv"
	"log"
	"path"
)

// 加载配置文件
func init() {
	if err := loadEnvFiles(); err != nil {
		log.Fatalf("配置文件加载失败: %v", err)
	}
}

func main() {
	log.Println("=== 程序开始执行 ===")

	// 从环境变量中读取配置
	log.Println("步骤1: 读取环境变量 PROCESS_TYPE")
	processType := utils.MustGetEnv("PROCESS_TYPE")
	log.Printf("✓ PROCESS_TYPE = '%s'", processType)

	// 读取科目配置文件
	log.Println("步骤2: 读取环境变量 SUBJECT_CONFIG_FILENAME")
	subjectConfigFilename := utils.MustGetEnv("SUBJECT_CONFIG_FILENAME")
	log.Printf("✓ SUBJECT_CONFIG_FILENAME = '%s'", subjectConfigFilename)

	log.Printf("步骤3: 读取科目配置文件 '%s'", subjectConfigFilename)
	subjectConfigsFromCSV := utils.ReadSubjectConfigsCSV(subjectConfigFilename)
	if subjectConfigsFromCSV == nil {
		log.Fatalf("✗ 读取科目配置文件失败，程序退出")
	}
	log.Printf("✓ 成功读取科目配置，共 %d 条记录", len(subjectConfigsFromCSV))

	// 获取本次处理根目录
	log.Println("步骤4: 读取环境变量 EXPORT_DATA_BATH_PATH")
	exportDataBathPath := utils.MustGetEnv("EXPORT_DATA_BATH_PATH")
	log.Printf("✓ EXPORT_DATA_BATH_PATH = '%s'", exportDataBathPath)

	dirPath := path.Join(exportDataBathPath, "export_data")
	log.Printf("✓ 计算得到处理目录路径 = '%s'", dirPath)

	// 根据配置类型执行不同的逻辑
	log.Printf("步骤5: 根据 processType '%s' 执行相应逻辑", processType)
	switch processType {
	case "TEST":
		log.Println("→ 执行测试文件名验证功能")
		jkd_import.TestIsValidFileName()
		log.Println("✓ 测试文件名验证功能执行完成")
	case "DOWNLOAD_JKD":
		downloadPath := path.Join(dirPath, "sourceData")
		log.Printf("→ 执行 JKD 题目下载功能")
		log.Printf("  下载目录: '%s'", downloadPath)
		log.Printf("  科目配置数量: %d", len(subjectConfigsFromCSV))
		jkd_dwld.QuestionDownloadAndUpZip(downloadPath, subjectConfigsFromCSV)
		log.Println("✓ JKD 题目下载功能执行完成")
	case "IMPORT_MGT":
		log.Printf("→ 执行 JKD 题库导入功能")
		log.Printf("  导入目录: '%s'", dirPath)
		log.Printf("  科目配置数量: %d", len(subjectConfigsFromCSV))
		jkd_import.QuestionsSetImport(dirPath, subjectConfigsFromCSV)
		log.Println("✓ JKD 题库导入功能执行完成")
	case "QUESTION_IMAGES_HANDLE":
		log.Printf("→ 执行 JKD 题目图片处理功能")
		log.Printf("  处理目录: '%s'", dirPath)
		jkd_image.QuestionImageHandle(dirPath)
		log.Println("✓ JKD 题目图片处理功能执行完成")
	case "GENERATE_CHAP_SQL":
		chapTreePath := path.Join(dirPath, "chapterTree")
		log.Printf("→ 执行 JKD 篇章节 SQL 生成功能")
		log.Printf("  章节树目录: '%s'", chapTreePath)
		jkd_chap_tree.ReadChapTreeCSVGenerateSQL(chapTreePath)
		log.Println("✓ JKD 篇章节 SQL 生成功能执行完成")
	default:
		log.Printf("✗ 未知的 processType: '%s'", processType)
		fmt.Println("Unknown processType:", processType)
	}

	log.Println("=== 程序执行完成 ===")
}

func loadEnvFiles() error {
	// 必需的配置文件
	requiredFiles := []string{
		".env",     // 系统配置文件
		".env.jkd", // JKD 配置必须存在
	}

	// 可选的配置文件
	var optionalFiles []string

	// 加载必需的配置文件
	for _, file := range requiredFiles {
		if err := godotenv.Load(file); err != nil {
			return fmt.Errorf("必需的配置文件 %s 未找到", file)
		}
	}

	// 加载可选的配置文件
	for _, file := range optionalFiles {
		if err := godotenv.Load(file); err != nil {
			log.Printf("可选配置文件 %s 未找到", file)
		}
	}

	return nil
}
